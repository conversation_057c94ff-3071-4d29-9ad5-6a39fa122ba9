from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import AzureChatOpenAI

import os
from dotenv import load_dotenv
load_dotenv()


if( os.environ.get("AZURE_OPENAI_API_KEY") is not None ):
    llm_AzureOpenAI = AzureChatOpenAI(
        azure_deployment="gpt-4.1",
        temperature=0.1,
        api_version="2024-05-01-preview"
    )
    """
    #Used it for testing purposes. With an own API key with free limited tier. 
    llm_freeGoogleGemini20Flash = ChatGoogleGenerativeAI(
        model="gemini-2.0-flash",
        max_tokens=None,
        timeout=None,
        max_retries=2,
    )
    """

    #----------------------------------------------------------------------------------------------------------------------
    #Set llm models here from the defined models from above!
    llm_AddContext=llm_AzureOpenAI