from langchain_openai import Azure<PERSON>penAIEmbeddings
from langchain_google_genai import GoogleGenerativeAIEmbeddings

from dotenv import load_dotenv
load_dotenv()


#embeddings_AzureOpenAI = AzureOpenAIEmbeddings(model="text-embedding-3-large")


#Used it for testing purposes. With an own API key with free limited tier. 
embeddings_GoogleFree = GoogleGenerativeAIEmbeddings(model="models/embedding-001")


#----------------------------------------------------------------------------------------------------------------------
#Set embedding models here from the defined models from above!

#IF YOU UPDATE EMBEDDING MODEL MAKE SURE TO UPDATE THE NUMBER OF DIMENSIONS AS WELL!
embeddings_num_dimensions_vectorStore=768
embeddings_VectorStore=embeddings_GoogleFree

embeddings_SemanticChunker=embeddings_GoogleFree