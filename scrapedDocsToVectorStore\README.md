# Preprocess and save scraped datas

## Table of Contents
1. [Introduction](#introduction)
2. [Installation](#installation)
3. [Usage](#usage)
4. [Code Structure](#code-structure)
5. [Workflow](#workflow)
6. [Known Issues](#known-issues)

## Introduction
This project component processes scraped website content, converts it into a structured format, and stores it in a vector store (currently using TimescaleDB).

## Installation
List the steps to set up the project locally:
1. Clone the repository and make sure you have Python installed.
   ```bash
   git clone https://github.com/Nemetschek-SE/sales-agent.git
   ```
2. Navigate to the project directory:
   ```bash
   cd ./scrapedDocsToVectorStrore
   ```
3. Create a virtual environment using Python:
   ```bash
   python -m venv .venv # .venv is the name of the virtual environment
   ```
4. Activate the virtual environment:
   - On Windows:
     ```bash
     .venv\Scripts\Activate.ps1
     ```
   - On macOS/Linux:
     ```bash
     source .venv/bin/activate
     ```
5. Install dependencies from `requirements.txt`:
   ```bash
   pip install -r requirements.txt
   ```
6. Set up API key (or and environment variables) in a `.env` file in the root directory of the project.
   .env file should contain the following variables:
   ```plaintext
   AZURE_OPENAI_API_KEY=
   OPENAI_API_VERSION=
   AZURE_OPENAI_ENDPOINT=

   POSTGRES_USER=
   POSTGRES_PASSWORD=
   POSTGRES_HOST=
   POSTGRES_PORT=
   POSTGRES_DB=
   VECTOR_STORE_COLLECTION="rag_chunks"

   MONGO_URL=
   MONGO_DATABASE_NAME=

   GOOGLE_API_KEY=
   ```

7. If you don't have a TimescaleDb instance with pgvector installed:

   #### Create a TimescaleDB instance and install the pgvector extension with the help of predefined scripts:
   Run the following command to initialize the Docker container:
   - For Windows:
   ```bash
   cd Docker
   .\DockerInit.bat
   cd ..
   ```
   - For Linux:
   ```bash
   cd Docker
   ./DockerInit.sh
   cd ..
   ```

   #### Or create a TimescaleDB instance manually with command line:
   ```bash
   docker build -t timescaledb-with-pgvector -f .\DockerImage\Dockerfile .
   docker-compose -f .\Docker\DockerCompose\docker-compose.yml up -d
   ```

8. Ensure you have a MongoDB instance running and set the connection details in the `.env` file. (And in the MongoDB there are scraped pages stored.)

## Usage
The input must be Markdown content retrieved from the database, and after processing, the data will be stored in a vector store.

### Setup
 -  **`LLM`** : You can set for the context collect the prefered LLM model at the bottom  of `llm.py` in `llm_AddContext` variable.
 -  **`Embedding`** : You can set for semantic embedding the embedding model that you prefer at the bottom  of `embedding.py` in `embeddings_VectorStore` variable.
 -  **`Spliting`** : If you want to change the splitter parameter you can do it in `functions.py` in the `addChunkListsTo` function.

### DocWithSource Structure

The `DocWithSource` class is a custom data structure used to represent a document along with its source and associated metadata. It is central to the workflow of processing and storing documents in the project.

#### Attributes

1. **`source`**
   - **Type**: `str`
   - **Description**: The source of the document (URL). This helps identify where the document originated from.

2. **`content`**
   - **Type**: `str`
   - **Description**: The full content of the document in its original form. For example, this could be the raw Markdown content of a web page.

3. **`chunkList`**
   - **Type**: `list[str]`
   - **Description**: A list of smaller chunks derived from the document's content. These chunks are created by splitting the document into manageable pieces, for storage in a vector store.

4. **`contextList`**
   - **Type**: `list[str]`
   - **Description**: A list of contextual information corresponding to each chunk in `chunkList`. Each context provides additional meaning or background for its associated chunk.

#### Purpose

The `DocWithSource` structure is designed to:
- Store and manage document data in a structured way.
- Facilitate workflows such as splitting documents into chunks, generating context for those chunks, and saving them to a vector store.
- Maintain a clear association between the document's source, its content, and the metadata (chunks and contexts) generated during processing.

## .env file config
POSTGRES_USER="your_user"
POSTGRES_PASSWORD="your_password"
POSTGRES_DB="your_database"
POSTGRES_HOST="localhost"
POSTGRES_PORT="5432"
POSTGRES_TABLE="rag_chunks"

MONGO_URL=
MONGO_DATABASE_NAME=
MONGO_COLLECTION=

GOOGLE_API_KEY=

AZURE_OPENAI_API_KEY=
OPENAI_API_VERSION=
AZURE_OPENAI_ENDPOINT=

## Code Structure
- `main.py`: Entry point of the application.
- `functions.py`: Contains core functions for processing documents.
- `embeddings.py`: Defines embedding models.
- `vectorStore.py`: Handles vector store operations.
- `llm.py`: Configures language models.
- `prompt.py`: Defines prompts for context generation.
- `requirements.txt`: Lists project dependencies.

## Workflow
1. Load documents using `loadScrapedPagesTo_DocWithSource` in `functions.py`.
2. Split documents into chunks using `addChunkListsTo`.
3. Generate context for chunks using `addContextLists`.
4. Save chunks and contexts to the vector store using `saveChunksToVectoreStore`.


### Visualization
A flowchart visualizing the workflow of the project:
![Flow](./Visualization/fromScrapedDatatoVectoreStore.png)


## Known Issues
- The LangChain connector for Timescale incorrectly assigns column sizes for semantic embedding models, causing issues with smaller embedding dimensions like 768 if not manually set.
- In `insertVectorStore`, use of `ensure_vector_index_hnsw` heavily use memory, so maybe have to make docker container with more memory.
