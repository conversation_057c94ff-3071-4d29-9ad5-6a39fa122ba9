$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "`n--- Applying Kubernetes Persistence Configuration ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\persistence")
    kubectl apply -f .
    Pop-Location
    Write-Host "Persistence configuration applied successfully."
}
catch {
    Write-Error "Error applying persistence configuration: $($_.Exception.Message)"
    exit 1
}
