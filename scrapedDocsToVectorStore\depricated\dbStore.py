from langchain_community.vectorstores.timescalevector import TimescaleVector
from embeddings import embeddings_VectorStore, embeddings_num_dimensions_vectorStore

from indexForVector import ensure_vector_index_ivfflat, ensure_vector_index_hnsw

import os
from dotenv import load_dotenv

load_dotenv()

# Retrieve variables from environment
TIMESCALE_USER = os.getenv("TIMESCALE_USER")
TIMESCALE_PASSWORD = os.getenv("TIMESCALE_PASSWORD")
TIMESCALE_HOST = os.getenv("TIMESCALE_HOST")
TIMESCALE_PORT = os.getenv("TIMESCALE_PORT")
TIMESCALE_NAME = os.getenv("TIMESCALE_NAME")

SERVICE_URL = f"postgresql://{TIMESCALE_USER}:{TIMESCALE_PASSWORD}@{TIMESCALE_HOST}:{TIMESCALE_PORT}/{TIMESCALE_NAME}"
# Initialize the embedding model
EMBEDDING_MODEL=embeddings_VectorStore

_vector_store_instance = {}

def getVectorStore(table_name):
    global _vector_store_instance
    if table_name not in _vector_store_instance:
        try:
            _vector_store_instance[table_name] = TimescaleVector(
                embedding=EMBEDDING_MODEL,
                service_url=SERVICE_URL,
                collection_name=table_name,
                num_dimensions=embeddings_num_dimensions_vectorStore
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
    return _vector_store_instance.get(table_name)


# Functions for the selected vector store
def addDocumentsToVectorStore(langcahinDocuments, table_name, create_index=True):
    vs = getVectorStore(table_name)
    if vs:
        vs.add_documents(langcahinDocuments)
        if create_index:
            ensure_vector_index_hnsw(table_name)
    else:
        print("[Warning] Vector store not available; documents not added.")