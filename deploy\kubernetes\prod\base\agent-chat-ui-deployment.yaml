apiVersion: apps/v1
kind: Deployment
metadata:
  name: agent-chat-ui
  labels:
    app: sales-agent
    component: chat-ui
    tier: frontend
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: chat-ui
      tier: frontend
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: chat-ui
        tier: frontend
        environment: production
    spec:
      containers:
        - name: agent-chat-ui
          image: agent-chat-ui:latest
          imagePullPolicy: Never
          env:
            - name: LANGGRAPH_API_URL
              value: http://langgraph-api:8123
          ports:
            - containerPort: 3000
              protocol: TCP
