# Local Development Setup

Follow these steps to run the app locally using Docker Desktop and Kubernetes.

## 1. Initial Setup

These steps need to be done only once:

1. **Install Docker Desktop**  
2. **Enable Kubernetes in Docker Desktop**  
   - Open Docker Desktop.
   - Go to **Settings** > **Kubernetes**.
   - Check **Enable Kubernetes** and click **Apply and restart**.

## 2. Configure API Keys

Edit `dev/base/api-secrets.yaml` and add your API keys:

- **LANGSMITH_API_KEY**: Required for running `langgraph-api` (even locally).
- **LANGSMITH_PROJECT**: The project name for traces at [smith.langchain.com](https://smith.langchain.com).
- **GOOGLE_API_KEY**: Obtain from [Google AI Studio](https://aistudio.google.com/app/apikey) (currently used for the model in the workflow and also in the RAG, can be changed for Azure OpenAI deployment once the workflow is updated).
- **GALILEO_API_KEY**: Required for logging at [Galileo](https://app.galileo.ai/)
## 3. Build Docker Images

Build all images the first time. Later, rebuild only those for projects you change.

### Pricing MCP Server

```bash
# From the repo's root
cd pricing
docker build -t mcp-pricing .
```

### Search MCP Server

```bash
# From the repo's root
cd search
docker build -t mcp-search .

# This builds the db image required for the Search MCP service
cd ./scrapedDocsToVectorStore/Docker
docker build -t timescaledb-with-pgvector -f ./DockerImage/Dockerfile .
```

### Agent Chat UI

Step 1:
```bash
# From the repo's root
cd agent-chat-ui
```
Step 2:
Create a .env with the following content
```env
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_ASSISTANT_ID=agent
```
Step 3:
```bash
docker build -t agent-chat-ui .
```

### Langgraph API

You need to switch the name of the Galileo logging session. Currently there is a hardcoded value that is test. This is in the graph.py file, in this line: ```galileo_context.start_session(name="test")```

```bash
# From the repo's root
cd agent
pip install 'langgraph-cli[inmem]'
# The following has to be done every time you change the workflow
pip install -e .
langgraph build -t sales-agent-api
```

## Build images for the jobs to fill dbs for the services
This includes a scraping job and a vectorizing job

### Scraping job
```bash
# From the repo's root
cd scraping
docker build -t scrapy-graphibot .
```

### Vectorizer pipeline job
```bash
# From the repo's root
cd scrapedDocsToVectorStore
docker build -t vectorizer-pipeline .
```

## 4. Start the Local Kubernetes Cluster

```bash
# From the repo's root
cd ./deploy/kubernetes/dev/persistence
kubectl apply -f .
```

```bash
# From the repo's root
cd ./deploy/kubernetes/dev/base
kubectl apply -f .
```

```bash
# From the repo's root
cd ./deploy/kubernetes/dev/jobs
kubectl apply -f mcp-pricing-init-sql-configmap.yaml
kubectl apply -f mcp-pricing-scrapy-job.yaml
# You can check if the job is in a Completed state with
kubectl get pods

# Run this to scrape all of the websites, wait for it to complete
kubectl apply -f scraped-websites-scrapy-job.yaml
# Check if the job is completed (for a full scrape it's ~3-4 min)
kubectl get pods

# Run this job to vectorize the scraped data, wait for it to complete
kubectl apply -f mcp-search-vectorizer-pipeline-job.yaml
# Check if the job is completed (for a full scrape with free Google API key it's ~20 minutes)
kubectl get pods
```

Currently the MCP Search secret for the correct table name has to be updated and the secret and deployment has to be reapplied.

## 5. Access Local Services

To access a service locally, use port forwarding. For example, to access the UI:

```bash
kubectl port-forward services/agent-chat-ui 3000:3000
```

List all available services:

```bash
kubectl get services
```

## 6. Clean Up

Delete all deployed Kubernetes resources when finished:

```bash
# From the repo's root
cd ./deploy/kubernetes/dev/jobs
kubectl delete -f .

# From the repo's root
cd ./deploy/kubernetes/dev/base
kubectl delete -f .
```

Only delete the following if you want to delete all data and rerun scraping and vectorizing jobs!!!
```bash
# From the repo's root
cd ./deploy/kubernetes/dev/persistence
kubectl delete -f .
```

## IMPORTANT NOTES for developing the project
- When making changes to a project (e.g. the LangGraph API), build the image for the changed project, then do [step 6](#6-clean-up), [step 4](#4-start-the-local-kubernetes-cluster) and [step 5](#5-access-local-services) in this order (or just delete all pods in that projects deployment if you know Kubernetes), this way the new images will be used with your changes!
- The pricing MCP server will respond with an error due to its database being empty on startup.
- I know this is tedious, will be working on it.
- For easier setup, the commands have been sorted into scripts in the `sales-agent/scripts` folder. In a separate folder are image builds, creating Kubernetes clusters, and deleting Kubernetes clusters.
- All docker image builds are in a separate file
- All job image builds are in a separate file
- There are separate scripts for starting the local Kubernetes cluster and for cleaning up the cluster
- There is a sccript, that runs all of the above steps in order, so you can just run `./sales-agent/scripts/full-deploy.ps1`
