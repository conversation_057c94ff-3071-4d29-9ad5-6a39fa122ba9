from langchain_core.prompts import ChatPromptTemplate

prompt_getConetxt = ChatPromptTemplate([
    ("system", "Please give a short succinct context to situate this chunk within the overall document for the purposes of improving search retrieval of the chunk. Answer only with the succinct context and nothing else."),
    ("user", "<document> {WHOLE_DOCUMENT} </document> "
    "Here is the chunk we want to situate within the whole document "
    "<chunk> {CHUNK_CONTENT} </chunk> ")
])