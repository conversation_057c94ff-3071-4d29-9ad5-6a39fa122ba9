import os
from mcp.server.fastmcp import Fast<PERSON><PERSON>, Context
from contextlib import asynccontextmanager
from collections.abc import Async<PERSON>terator
from dataclasses import dataclass
from dotenv import load_dotenv

from search.rephrase import RePhraseQueryRetriever
from search.report import ReportGenerator
from search.rephraser_vectorsearch import TimescaleVectorSearch


# Load environment variables from .env file
load_dotenv()

# Create an MCP server
mcp = FastMCP("Product data service")

@dataclass
class AppContext:
    vector_search: TimescaleVectorSearch
    retriever: RePhraseQueryRetriever
    report_generator: ReportGenerator

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with database connections"""
    vector_search = TimescaleVectorSearch()
    retriever = ReportGenerator()
    report_generator = ReportGenerator()
    try:
        yield AppContext(vector_search=vector_search, retriever=retriever, report_generator=report_generator)
    finally:
        pass

# Pass lifespan to server
mcp = FastMCP("Pricing Service", lifespan=app_lifespan)

@mcp.tool()
def search_context(
    ctx: Context,
    user_query: str,
) -> str:
    """
    Search for information based on a user query.
    
    Args:
        user_query: The original user query to be semantically searched.
    
    Returns:
        A report summarizing the search results.
    """
    vector_search = ctx.request_context.lifespan_context.vector_search
    retriever = ctx.request_context.lifespan_context.retriever
    report_generator = ctx.request_context.lifespan_context.report_generator

    # Generate multiple rephrased variants of the original user query
    queries = retriever.rephrase(
        user_query=user_query,
        num_variants=5,
        max_length=200
    )

    # Perform a similarity search using all the rephrased queries
    results = vector_search.search_multiple_queries(queries=queries, k=5)

    # Generate a report based on the original user query and the search results
    report = report_generator.generate_report(user_query, results)

    return report

if __name__ == "__main__":
    mcp.run()