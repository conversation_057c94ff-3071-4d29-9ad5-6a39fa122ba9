apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-pricing
  labels:
    app: sales-agent
    component: pricing
    tier: backend
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: pricing
      tier: backend
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: pricing
        tier: backend
        environment: production
    spec:
      containers:
        - image: mcp-pricing:latest
          name: mcp-pricing
          imagePullPolicy: Never
          envFrom:
            - secretRef:
                name: mcp-pricing-postgres-credentials
          ports:
            - containerPort: 8000
              protocol: TCP
