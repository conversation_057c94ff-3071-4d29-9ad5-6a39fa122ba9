import pymongo
from pymongo import MongoClient

import os
from dotenv import load_dotenv

from src.utils.doc_with_source import DocWithSource

# .env fájl betöltése
load_dotenv()


class MDMongoLoader:
    def __init__(self, mongo_url=os.getenv("MONGO_URL"), database_name=os.getenv("MONGO_DATABASE_NAME")):
        self.mongo_url = mongo_url
        self.database_name = database_name

    def get_latest_collection_name(self):
        """    
        Returns:
            Latest collection name or empty string if no collections found
        """
        try:
            # Connect to MongoDB
            client = MongoClient(self.mongo_url)
            db = client[self.database_name]
            

            # Get all collection names
            collections = db.list_collection_names()

            # Filter and find the latest collection
            valid_collections = [
                name for name in collections 
                if name.startswith("graphibot_") and len(name) == 25
            ]
            
            if not valid_collections:
                return ""
                
            # Sort collections by name (since format YYYYMMDD_HHMMSS will sort chronologically)
            latest_collection = sorted(valid_collections)[-1]
            
            return latest_collection
            
        except Exception as e:
            print(f"Error connecting to MongoDB: {e}")
            return ""
        finally:
            client.close()

    def loadAllMarkDown_fromMongoDB(self) -> list[DocWithSource]:
        collectionName = os.getenv("MONGO_COLLECTION_NAME", self.get_latest_collection_name())

        # MongoDB kapcsolódás
        client = pymongo.MongoClient(self.mongo_url)
        db = client[self.database_name]
        collection = db[collectionName]

        # Retrieve all documents from the collection
        documents = collection.find()

        datas=[]

        # Process each document
        for doc in documents:
            document = DocWithSource(
                source=doc.get("url", ""),
                content = doc.get("md", ""),  # Assuming the Markdown content is stored in a field called 'content'
                chunkList=[],
                contextList=[]
            )
            
            datas.append(document)

        return datas