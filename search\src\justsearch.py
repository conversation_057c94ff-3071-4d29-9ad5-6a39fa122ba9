from vectorsearch import TimescaleVectorSearch
from dotenv import load_dotenv

# Load environment variables from the .env file into the environment
load_dotenv()

user_query = "What is Graphisoft Forward?"

# Initialize the TimescaleVectorSearch instance to perform vector-based similarity search
ts_vector_search = TimescaleVectorSearch()
# Perform a similarity search using the user query
results = ts_vector_search.search_query(user_query, k=5)   
# Output the search results
for doc, score in results:
    print(f"Document: {doc.page_content}, Score: {score}")
