from langchain.schema import Document

from src.utils.doc_with_source import DocWithSource

def mergeChunkAndContext(chunk, context):
    return f"[Context]:{context}\n[Content]:{chunk}"

def ConvertDocsToLangchainDocuments(documents: DocWithSource):
    langchainDocuments = []
    for doc in documents:
        for chunk, context in zip(doc.chunkList,doc.contextList):
            langchainDocuments.append(Document(page_content=mergeChunkAndContext(chunk, context), metadata={"source": doc.source}))

    return langchainDocuments

def ConvertDocsToLangchainDocumentsWithoutContext(documents: DocWithSource):
    langchainDocuments = []
    for doc in documents:
        for chunk in doc.chunkList:
            langchainDocuments.append(Document(page_content=chunk, metadata={"source": doc.source}))

    return langchainDocuments