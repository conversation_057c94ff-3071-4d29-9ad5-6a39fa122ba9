apiVersion: apps/v1
kind: Deployment
metadata:
  name: langgraph-redis
  labels:
    app: sales-agent
    component: langgraph
    tier: cache
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: langgraph
      tier: cache
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: langgraph
        tier: cache
        environment: production
    spec:
      containers:
        - name: langgraph-redis
          image: redis:6
          livenessProbe:
            exec:
              command: ["redis-cli", "ping"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          readinessProbe:
            exec:
              command: ["redis-cli", "ping"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          ports:
            - containerPort: 6379
              protocol: TCP
