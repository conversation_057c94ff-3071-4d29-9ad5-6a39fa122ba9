import os
from typing import List, Tuple
from langchain_core.documents import Document
from langchain_community.vectorstores.timescalevector import TimescaleVector
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import heapq


class TimescaleVectorSearch:
    def __init__(self):
        # Load environment variables
        self.service_url = os.environ.get("TIMESCALE_SERVICE_URL", "")
        self.collection_name = os.environ.get("TIMESCALE_COLLECTION_NAME", "")
        self.embedding_model = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        # Initialize TimescaleVector
        self.vector_store = TimescaleVector(
            service_url=self.service_url,
            embedding=self.embedding_model,
            collection_name=self.collection_name,
            num_dimensions=768
        )

    def embed_query(self, query: str) -> List[float]:
        """Generate embedding for a single query."""
        return self.embedding_model.embed_query(query)

    def search_similar_documents(self, query: str, k: int = 5) -> List[Tuple[Document, float]]:
        """Perform vector similarity search using Timescale."""
        embedded_query = self.embed_query(query)
        return self.vector_store.similarity_search_with_score_by_vector(embedding=embedded_query, k=k)

    def search_multiple_queries(self, queries: List[str], k: int = 5, top_n: int = 5) -> List[Tuple[Document, float]]:
        """Run similarity search for multiple queries and return deduplicated top results."""
        docs_with_score = []
        for query in queries:
            results = self.search_similar_documents(query, k)
            docs_with_score.extend(results)

        return self._deduplicate_documents(docs_with_score, top_n)

    def _deduplicate_documents(self, docs_with_score: List[Tuple[Document, float]], top_n) -> List[Tuple[Document, float]]:
        """Remove duplicate documents, keeping the one with the highest score."""
        unique_docs = {}
        for doc, score in docs_with_score:
            content = doc.page_content
            # If content is new or has a higher score, store/update it
            if content not in unique_docs or score > unique_docs[content][1]:
                unique_docs[content] = (doc, score)

        # Sort by score descending and return top_n results
        return heapq.nsmallest(top_n, unique_docs.values(), key=lambda x: x[1])