# Kiindulási alap: TimescaleDB HA image PostgreSQL 17-tel
FROM timescale/timescaledb-ha:pg17

# Root jogosultságra váltás a telepítéshez
USER root

# Frissítjük a csomaglistát és telepítjük a pgvector csomagot
RUN apt-get update && \
    apt-get install -y postgresql-17-pgvector && \
    rm -rf /var/lib/apt/lists/*

COPY DockerCompose/init-pgvector.sh /docker-entrypoint-initdb.d/init-pgvector.sh

# Visszaállítás postgres felhasználóra
USER postgres