from langchain_community.vectorstores.timescalevector import TimescaleVector
from langchain_postgres import PGVector

from src.utils.index_for_vector import ensure_vector_index_hnsw
from src.utils.generate_table_name import generate_table_name

import os
from dotenv import load_dotenv
load_dotenv()

class PGVectorStore:
    def __init__(self, embedding_model, service_url, num_dimensions, table_name):
        self.embedding_model = embedding_model
        self.service_url = service_url
        self.num_dimensions = num_dimensions
        self.table_name = table_name
        try:
            self.vector_store = PGVector(
                embeddings=self.embedding_model,
                connection=self.service_url,
                collection_name=self.table_name,
                create_extension=True,
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None

    def add_documents(self, langchain_documents, create_index=True):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents)
        else:
            print("[Warning] Vector store not available; documents not added.")

    def create_index_to_vectors(self):
        print("I don't create index in PGVector")

class TimescaleDB:
    def __init__(self, embedding_model, service_url, num_dimensions, table_name):
        self.embedding_model = embedding_model
        self.service_url = service_url
        self.num_dimensions = num_dimensions
        self.table_name = table_name
        try:
            self.vector_store = TimescaleVector(
                embedding=self.embedding_model,
                service_url=self.service_url,
                collection_name=self.table_name,
                num_dimensions=self.num_dimensions
            )
        except Exception as e:
            print(f"[Warning] Could not initialize vector store: {e}")
            self.vector_store = None

    def add_documents(self, langchain_documents, create_index=True):
        if self.vector_store:
            self.vector_store.add_documents(langchain_documents)
            if create_index:
                ensure_vector_index_hnsw(self.table_name, self.service_url)
        else:
            print("[Warning] Vector store not available; documents not added.")
    
    def create_index_to_vectors(self):
        if self.vector_store:
            ensure_vector_index_hnsw(self.table_name, self.service_url)
        else:
            print("[Warning] Vector store not available; index not created.")

class VectorStoreManager:
    def __init__(self, embedding_model, service_url, num_dimensions, table_name=os.getenv("VECTOR_STORE_COLLECTION")):
        self.db = TimescaleDB(embedding_model, service_url, num_dimensions, table_name)

    def add_documents(self, langchain_documents, create_index=True):
        self.db.add_documents(langchain_documents, create_index=create_index)

    def create_index_to_vectors(self):
        self.db.create_index_to_vectors()