import os
from dotenv import load_dotenv

load_dotenv()

def get_vector_store_service_url():
    """
    Constructs the service URL for the Timescale database using environment variables.
    
    Returns:
        str: The service URL for the Timescale database.
    """
    TIMESCALE_USER = os.getenv("POSTGRES_USER")
    TIMESCALE_PASSWORD = os.getenv("POSTGRES_PASSWORD")
    TIMESCALE_HOST = os.getenv("POSTGRES_HOST")
    TIMESCALE_PORT = os.getenv("POSTGRES_PORT")
    TIMESCALE_NAME = os.getenv("POSTGRES_DB")

    SERVICE_URL = f"postgresql://{TIMESCALE_USER}:{TIMESCALE_PASSWORD}@{TIMESCALE_HOST}:{TIMESCALE_PORT}/{TIMESCALE_NAME}"
    return SERVICE_URL