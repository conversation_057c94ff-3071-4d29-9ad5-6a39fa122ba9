import datetime

import pymongo
from pymongo import MongoClient

from langchain_text_splitters import RecursiveCharacterTextSplitter

from langchain.schema import Document

from prompt import prompt_getConetxt
from dbStore import addDocumentsToVectorStore

from tqdm import tqdm

import os

if( os.environ.get("AZURE_OPENAI_API_KEY") is not None ):
    from llm import llm_AddContext

from dotenv import load_dotenv

# .env fájl betöltése
load_dotenv()

class DocWithSource:
    def __init__(self, content, source, chunkList, contextList):
        self.source = source
        self.content = content
        self.chunkList = chunkList
        self.contextList = contextList

def mergeChunkAndContext(chunk, context):
    return f"[Context]:{context}\n[Content]:{chunk}"

def ConvertDocsToLangchainDocuments(documents: DocWithSource):
    langcahinDocuments = []
    for doc in documents:
        for chunk, context in zip(doc.chunkList,doc.contextList):
            langcahinDocuments.append(Document(page_content=mergeChunkAndContext(chunk, context), metadata={"source": doc.source}))

    return langcahinDocuments

def ConvertDocsToLangchainDocumentsWithoutContext(documents: DocWithSource):
    langcahinDocuments = []
    for doc in documents:
        for chunk in doc.chunkList:
            langcahinDocuments.append(Document(page_content=chunk, metadata={"source": doc.source}))

    return langcahinDocuments

def generate_table_name(base_name="mytable"):
    # Get current time in format YYYYMMDD_HHMMSS
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    return f"{base_name}_{timestamp}"

def saveChunksToVectoreStore(documents: list[DocWithSource], ConvertDocsToLangchainDocumentsFunction=ConvertDocsToLangchainDocuments, table_name=generate_table_name("rag"), batch_size=100):
    langcahinDocuments = ConvertDocsToLangchainDocumentsFunction(documents)

    num_batches = (len(langcahinDocuments) + batch_size - 1) // batch_size
    for i in tqdm(range(0, len(langcahinDocuments), batch_size), desc="Uploading batches", total=num_batches):
        batch = langcahinDocuments[i:i+batch_size]
        addDocumentsToVectorStore(batch, table_name, create_index=False)

    # Ensure the vector index is created after all documents are added
    from indexForVector import ensure_vector_index_hnsw
    ensure_vector_index_hnsw(table_name)

def saveChunksToVectoreStoreWithContext(documents: list[DocWithSource], table_name=generate_table_name("rag")):
    saveChunksToVectoreStore(documents, ConvertDocsToLangchainDocumentsFunction=ConvertDocsToLangchainDocuments, table_name=table_name)

def saveChunksToVectoreStoreWithoutContext(documents: list[DocWithSource], table_name=generate_table_name("rag")):
    saveChunksToVectoreStore(documents, ConvertDocsToLangchainDocumentsFunction=ConvertDocsToLangchainDocumentsWithoutContext, table_name=table_name)

def addContextFromChunk(chunk, wholeDocument):
        print("addedd context")
        message=prompt_getConetxt.invoke({"WHOLE_DOCUMENT": wholeDocument, "CHUNK_CONTENT": chunk})
        response=llm_AddContext.invoke(message)
        return response.content


def addContextLists(docWithChunkList: list[DocWithSource]):
    for doc in docWithChunkList:
        wholeDocument=doc.content

        for chunk in doc.chunkList:
            doc.contextList.append(addContextFromChunk(chunk, wholeDocument))

def addChunkListsTo(docsWithContent: list[DocWithSource], chunk_size=2000, chunk_overlap=400):
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=chunk_size, chunk_overlap=chunk_overlap)

    for doc in docsWithContent:
        doc.chunkList=text_splitter.split_text(doc.content)

def addWholeDocumentAsChunkListsTo(docsWithContent: list[DocWithSource]):
    for doc in docsWithContent:
        doc.chunkList=doc.content

def get_latest_collection_name(mongo_url: str, database_name: str):
    """    
    Returns:
        Latest collection name or empty string if no collections found
    """
    try:
        # Connect to MongoDB
        client = MongoClient(mongo_url)
        db = client[database_name]
        

        # Get all collection names
        collections = db.list_collection_names()

        # Filter and find the latest collection
        valid_collections = [
            name for name in collections 
            if name.startswith("graphibot_") and len(name) == 25
        ]
        
        if not valid_collections:
            return ""
            
        # Sort collections by name (since format YYYYMMDD_HHMMSS will sort chronologically)
        latest_collection = sorted(valid_collections)[-1]
        
        return latest_collection
        
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        return ""
    finally:
        client.close()

def loadAllMarkDown_fromMongoDB():
    mongoURL = os.getenv("MONGO_URL")
    databaseName = os.getenv("MONGO_DATABASE_NAME")

    collectionName = get_latest_collection_name(mongoURL, databaseName)

    # MongoDB kapcsolódás
    client = pymongo.MongoClient(mongoURL)
    db = client[databaseName]
    collection = db[collectionName]

    # Retrieve all documents from the collection
    documents = collection.find()

    datas=[]

    # Process each document
    for doc in documents:
        document = DocWithSource(
            source=doc.get("url", ""),
            content = doc.get("md", ""),  # Assuming the Markdown content is stored in a field called 'content'
            chunkList=[],
            contextList=[]
        )
        
        datas.append(document)

    return datas

def loadSampleMD(markdown_path = "./response.md"):
    
    with open(markdown_path, 'r', encoding='utf-8') as f:
        markdown_content = f.read()

    document = DocWithSource(
        content=markdown_content,
        source="url.hu",
        chunkList=[],
        contextList=[]
    )
    documents=[]
    documents.append(document)

    return documents

#---------------------------------------------------------------------------------------------
# Add to this var the method how you get the documents that store the page contents
# !!!!!!!!!Constaraint: The method you set return a list of DocWithSource objects!!!!!!!!!
loadScrapedPagesTo_DocWithSource=loadAllMarkDown_fromMongoDB