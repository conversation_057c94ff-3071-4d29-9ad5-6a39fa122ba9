import os

from langchain_openai import AzureChatOpenAI

import os
from dotenv import load_dotenv
load_dotenv()

class AzureOpenAI:
    def __init__(self, deployment="gpt-4.1", api_key=os.environ.get("AZURE_OPENAI_API_KEY",""), api_endpoint=os.environ.get("AZURE_OPENAI_ENDPOINT",""), api_version=os.environ.get("OPENAI_API_VERSION", "2024-08-01"), temperature:float=0.1):
        self.deployment = deployment
        self.api_key = api_key
        self.api_endpoint = api_endpoint
        self.api_version = api_version
        self.temperature = temperature
        self.llm = AzureChatOpenAI(
            azure_deployment=self.deployment,
            temperature=self.temperature,
            api_version=self.api_version,
            #TODO: make endpoint changeable
        )

class ContextLLM:
    def __init__(self, llm=AzureOpenAI()):
        self.llm = llm.llm