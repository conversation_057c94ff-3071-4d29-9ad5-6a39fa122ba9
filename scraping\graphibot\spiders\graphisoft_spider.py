import scrapy
import logging
from markdownify import markdownify

class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(scrapy.Spider):
    name = "graphibot"
    start_urls = ["https://www.graphisoft.com/"]
    allowed_domains = ["www.graphisoft.com", "graphisoft.com"]
    custom_settings = {
        "DOWNLOADER_MIDDLEWARES": {
           "graphibot.middlewares.IgnoreSubdomainDownloadMiddleware": 543,
           "graphibot.middlewares.IgnoreCountryCodeDownloadMiddleware": 544,
           "graphibot.middlewares.IgnoreNonHtmlDownloadMiddleware": 545,
        },
        "ITEM_PIPELINES": {
            "graphibot.pipelines.MongoDBPipeline": 100,
        },
    }

    def parse(self, response):
        try:
            markdown = markdownify(response.text)
        except Exception as e:
            message = f"Error converting to Markdown: {e}"
            markdown = message
            logging.error(message)

        yield {
            "url": response.url,
            "status": response.status,
            "headers": dict(response.headers),
            "text": response.text,
            "md": markdown,
            "encoding": response.encoding,
        }

        for href in response.css("a::attr(href)").getall():
            absolute_url = response.urljoin(href)
            if absolute_url.startswith("http"):
                yield response.follow(href, callback=self.parse)
            else:
                logging.info(f"Ignored non-HTTP URL: {absolute_url}")
