# Semantic Search & Report Generation System

This project provides an end-to-end pipeline for semantic search and AI-powered report generation. It leverages advanced LLMs for query optimization, vector search, and structured reporting, using Timescale's vector database for efficient retrieval.


## Project Structure

- **`rephrase.py`**:
    Query rephraser using Azure OpenAI to generate multiple optimized search variants.
- **`rephraser_vectorsearch.py`**:
    Timescale vector search integration for RAG pipeline, with Google embeddings; handles deduplication.
- **`report.py`**:
    Azure-powered report generator producing Markdown with references and links.
- **`search.py`**:
    Main pipeline orchestrator: rephrases, searches, aggregates, and generates reports.
- **`requirements.txt`**:
    Lists all Python dependencies.
- **`vectorsearch.py`**:
    Timescale vector search integration for agent tool, with Google embeddings.
- **`justsearch.py`**:
    Simulate agent tool: vectorsearch and see the results.


## Features

- **Multi-LLM Integration**
    - Azure OpenAI (`gpt-4.1`) for query rephrasing and report generation.
    - Google AI embeddings (`models/embedding-001`) for vector search.

- **Advanced Semantic Search**
    - Generates multiple phrasings of each query to maximize recall.
    - Runs multiple vector searches and aggregates results.
    - Deduplicates documents based on content and score.

- **Structured, Reference-Aware Reporting**
    - Produces Markdown reports with superscripted references.
    - Returns results as validated JSON with report and links.
    - Strictly uses only provided context.

## Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd sales-agent/search
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up API keys in .env file:
    ```bash
    GOOGLE_API_KEY=<your-google-api-key>
    VECTOR_STORE_URL=<your-timescale-service-url>
    VECTOR_STORE_COLLECTION="rag_chunks"
    
    AZURE_OPENAI_API_KEY=<your-azure-openai-api-key>
    AZURE_OPENAI_ENDPOINT=<your-azure-openai-endpoint>
    OPENAI_API_VERSION=<your-openai-api-version>
    ```

## Run

After completing the installation and setting up the dependencies, you can run the pipeline using the following command:
```bash
python search.py
```
This will:
1. Rephrase the user query into multiple optimized variants.
2. Search the Timescale vector database for relevant documents.
3. Deduplicate and select the top results.
4. Generate a Markdown report with references, returned as JSON.
