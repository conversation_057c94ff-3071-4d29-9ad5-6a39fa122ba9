apiVersion: apps/v1
kind: Deployment
metadata:
  name: langgraph-postgres
  labels:
    app: sales-agent
    component: langgraph
    tier: db
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: langgraph
      tier: db
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: langgraph
        tier: db
        environment: production
    spec:
      containers:
        - name: langgraph-postgres
          image: postgres:16
          envFrom:
            - secretRef:
                name: langgraph-postgres-credentials
          livenessProbe:
            exec:
              command: ["pg_isready", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          readinessProbe:
            exec:
              command: ["pg_isready", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          ports:
            - containerPort: 5432
              protocol: TCP
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: langgraph-data
      volumes:
        - name: langgraph-data
          persistentVolumeClaim:
            claimName: langgraph-data
