$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "`n--- Deleting Kubernetes resources: base ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\base")
    kubectl delete -f .
    Pop-Location
    Write-Host "Resources from deploy\kubernetes\dev\base deleted successfully."
}
catch {
    Write-Error "Error deleting base resources: $($_.Exception.Message)"
    exit 1
}
