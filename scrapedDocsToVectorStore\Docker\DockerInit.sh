#!/bin/bash

# A script leáll, ha b<PERSON><PERSON><PERSON><PERSON> parancs hibával fut le
set -e

echo "Building the Docker image..."
# A build kontextus a jelen<PERSON>i mappa (.), a Dockerfile helyét -f-fel adjuk meg
docker build -t timescaledb-with-pgvector -f ./DockerImage/Dockerfile .
echo "Docker build successful!"

echo "Starting container with Docker Compose..."
# A docker compose (szóközzel) a modern szintaxis, ez így helyes
docker compose -f ./DockerCompose/docker-compose.yml up -d
echo "Docker Compose up successful!"

echo "Docker container started successfully!"