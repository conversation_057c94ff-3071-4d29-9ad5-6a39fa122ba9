apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-search-timescale
  labels:
    app: sales-agent
    component: search
    tier: db
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: search
      tier: db
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: search
        tier: db
        environment: production
    spec:
      containers:
        - name: mcp-search-timescale
          image: timescaledb-with-pgvector:latest
          imagePullPolicy: Never
          envFrom:
            - secretRef:
                name: mcp-search-timescale-credentials
          ports:
            - containerPort: 5432
              protocol: TCP
          volumeMounts:
            - mountPath: /home/<USER>/pgdata/data
              name: mcp-search-data
          securityContext:
            runAsUser: 0
            runAsGroup: 0
      volumes:
        - name: mcp-search-data
          persistentVolumeClaim:
            claimName: mcp-search-data
