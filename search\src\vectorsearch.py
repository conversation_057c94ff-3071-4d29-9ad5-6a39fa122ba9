from typing import List, <PERSON><PERSON>
from langchain_core.documents import Document
from langchain_community.vectorstores.timescalevector import TimescaleVector
from langchain_google_genai import GoogleGenerativeAIEmbeddings

import os
from dotenv import load_dotenv

load_dotenv()



class TimescaleVectorSearch:
    def __init__(self, service_url= os.environ.get("VECTOR_STORE_URL","")):
        # Load environment variables
        self.service_url = service_url
        self.collection_name = os.environ.get("VECTOR_STORE_COLLECTION", "rag_chunks")
        self.embedding_model = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        # Initialize TimescaleVector
        self.vector_store = TimescaleVector(
            service_url=self.service_url,
            embedding=self.embedding_model,
            collection_name=self.collection_name,
            num_dimensions=768
        )
    
    def search_query(self, query: str, k: int = 10) -> List[Tuple[Document, float]]:
        """Run similarity search for multiple queries and return deduplicated top results."""
        embedded_query = self.embedding_model.embed_query(query)
        docs_with_score = self.vector_store.similarity_search_with_score_by_vector(embedding=embedded_query, k=k)
        return docs_with_score
    