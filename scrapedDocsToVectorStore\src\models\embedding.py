from langchain_openai import AzureOpenAIEmbeddings
from langchain_google_genai import GoogleGenerativeAIEmbeddings

from dotenv import load_dotenv
load_dotenv()

class GoogleEmbedding:
    def __init__(self, model_name: str = "models/embedding-001"):
        self.embedding_model =  GoogleGenerativeAIEmbeddings(
            model=model_name
            )
        
class AzureEmbedding:
    def __init__(self, model_name: str = "text-embedding-3-large"):
        self.embedding_model = AzureOpenAIEmbeddings(
            model=model_name
            )
        
class EmbeddingModelForVectorStore:
    def __init__(self, embedding_model=GoogleEmbedding("models/embedding-001")):
        self.embedding_model = embedding_model.embedding_model