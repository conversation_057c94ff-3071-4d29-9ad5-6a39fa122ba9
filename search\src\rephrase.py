from langchain_openai import AzureChatOpenAI

class RePhraseQueryRetriever:
    def __init__(
        self,
        system_prompt = 
            """You are a query optimization engine for RAG search. Given a user query, generate as few distinct, high-quality vectorstore search queries as possible (maximum limit is {num_variants}). 
            Only add more if they are truly distinct and useful.
            Each variant should:
            1. Focus on key entities and relationships.
            2. Remove conversational fluff that does not affect the core question.
            3. Maintain the original search intent.
            4. Use alternative phrasings.

            Return ONLY a pipe-separated list of the variants, with no commentary or extra text."""
    ):
        self.llm = AzureChatOpenAI(azure_deployment="gpt-4.1",temperature=0.1)
        self.system_prompt = system_prompt

    def rephrase(
        self,
        user_query,
        num_variants = 5,
        max_length = 64
    ):
        try:
            response = self.llm.invoke(
                [
                    (
                        "system",
                        self.system_prompt.format(num_variants=num_variants)
                    ), 
                    (
                        "user",
                        user_query
                    ),
                ]
            )

            # Split the model response by pipe ('|'), clean each variant, strip to max_length
            processed = [
                q.strip()[:max_length]
                for q in response.content.split("|")
                if q.strip()
            ]
            # Return the top `num_variants` responses or fall back to the original query
            return processed[:num_variants] or [user_query]

        except Exception as e:
            print(f"Rephrasing error: {e}")
            return [user_query]
