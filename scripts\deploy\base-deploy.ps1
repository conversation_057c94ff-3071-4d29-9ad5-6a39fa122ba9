$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "`n--- Applying Kubernetes Base Configuration ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\base")
    kubectl apply -f .
    Pop-Location
    Write-Host "Base configuration applied successfully."
}
catch {
    Write-Error "Error applying base configuration: $($_.Exception.Message)"
    exit 1
}
