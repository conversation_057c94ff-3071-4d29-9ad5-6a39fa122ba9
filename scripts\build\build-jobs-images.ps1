# Set error action to stop on any errors
$ErrorActionPreference = "Stop"

# Define the root directory of your sales-agent repository.
# Assuming this script is placed in 'sales-agent/scripts',
# $PSScriptRoot points to 'sales-agent/scripts'.
# We need to go up one level to get to 'sales-agent'.
$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName

Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "Starting Docker image build process for jobs..."

# --- Scraping Job ---
Write-Host "`n--- Building Scraping Job Image (scrapy-graphibot) ---"
try {
    Push-Location (Join-Path $salesAgentRoot "scraping")
    docker build -t scrapy-graphibot .
    Pop-Location
    Write-Host "Scraping Job image built successfully."
}
catch {
    Write-Host "Error building Scraping Job image: $($_.Exception.Message)"
    exit 1
}

# --- Vectorizer Pipeline Job ---
Write-Host "`n--- Building Vectorizer Pipeline Job Image (vectorizer-pipeline) ---"
try {
    Push-Location (Join-Path $salesAgentRoot "scrapedDocsToVectorStore")
    docker build -t vectorizer-pipeline .
    Pop-Location
    Write-Host "Vectorizer Pipeline Job image built successfully."
}
catch {
    Write-Host "Error building Vectorizer Pipeline Job image: $($_.Exception.Message)"
    exit 1
}

Write-Host "`nAll specified Docker images for jobs have been processed."
