from functions import loadScrapedPagesTo_DocWithSource
from functions import addChunkListsTo, addWholeDocumentAsChunkListsTo
from functions import addContextLists
from functions import saveChunksToVectoreStoreWithoutContext, saveChunksToVectoreStoreWithContext
from functions import generate_table_name

def pipeline_without_context_chunk_size_2000():
    docs = loadScrapedPagesTo_DocWithSource()
    addChunkListsTo(docs, chunk_size=2000, chunk_overlap=400)
    print("Adding chunks without context")
    saveChunksToVectoreStoreWithoutContext(docs)

def pipeline_without_context_chunk_size_800():
    docs = loadScrapedPagesTo_DocWithSource()
    addChunkListsTo(docs, chunk_size=800, chunk_overlap=160)
    saveChunksToVectoreStoreWithoutContext(docs)

def pipeline_WITH_CONTEXT_chunk_size_2000():
    docs = loadScrapedPagesTo_DocWithSource()
    addChunkListsTo(docs, chunk_size=2000, chunk_overlap=400)
    addContextLists(docs)
    saveChunksToVectoreStoreWithContext(docs)

def pipeline_without_context_whole_documents():
    docs = loadScrapedPagesTo_DocWithSource()
    addWholeDocumentAsChunkListsTo(docs)
    saveChunksToVectoreStoreWithoutContext(docs)