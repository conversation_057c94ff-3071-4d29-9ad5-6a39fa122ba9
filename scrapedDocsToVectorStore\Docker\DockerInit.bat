@echo off
REM Build the Docker image
docker build -t timescaledb-with-pgvector -f .\DockerImage\Dockerfile .
IF %ERRORLEVEL% NEQ 0 (
    echo Docker build failed!
    exit /b %ERRORLEVEL%
)

REM Start the container using docker-compose
docker-compose -f .\DockerCompose\docker-compose.yml up -d
IF %ERRORLEVEL% NEQ 0 (
    echo Docker Compose up failed!
    exit /b %ERRORLEVEL%
)

echo Docker container started successfully!
pause