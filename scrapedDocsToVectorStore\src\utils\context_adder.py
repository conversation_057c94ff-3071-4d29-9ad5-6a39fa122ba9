from src.utils.doc_with_source import DocWithSource
from src.models.llm import ContextLLM

class ContextAdder:
    def __init__(self, prompt_getConetxt, ContextLLM=ContextLLM()):
        self.prompt_getConetxt = prompt_getConetxt
        self.ContextLLM = ContextLLM

        # Function to add context from a chunk to the whole document

    def addContextFromChunk(self, chunk, wholeDocument):
            print("added context")
            message=self.prompt_getConetxt.invoke({"WHOLE_DOCUMENT": wholeDocument, "CHUNK_CONTENT": chunk})
            response=self.ContextLLM.invoke(message)
            return response.content

    def addContextLists(self, docWithChunkList: list[DocWithSource]):
        for doc in docWithChunkList:
            wholeDocument=doc.content

            for chunk in doc.chunkList:
                doc.contextList.append(self.addContextFromChunk(chunk, wholeDocument))