from rephrase import Re<PERSON><PERSON><PERSON><PERSON>ueryRetriever
from report import ReportGenerator
from rephraser_vectorsearch import TimescaleVectorSearch
from dotenv import load_dotenv

# Load environment variables from the .env file into the environment
load_dotenv()

# The original user query to be semantically searched
user_query = "What is Graphisoft Forward?"

# Initialize the rephrasing retriever
retriever = RePhraseQueryRetriever()

# Generate multiple rephrased variants of the original user query
queries = retriever.rephrase(
    user_query=user_query,
    num_variants=5,
    max_length=200
)


# Initialize the TimescaleVectorSearch instance to perform vector-based similarity search
ts_vector_search = TimescaleVectorSearch()


# Perform a similarity search using all the rephrased queries
results = ts_vector_search.search_multiple_queries(queries, k=5)

# Initialize a report generator to summarize or format the retrieved documents
report_generator = ReportGenerator()

# Generate a report based on the original user query and the search results
report = report_generator.generate_report(user_query, results)

# Output the generated report
print(report)