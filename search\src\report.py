from typing import List
from langchain_core.prompts import ChatPromptTemplate
from pydantic import BaseModel
from langchain_openai import AzureChatOpenAI


class AnswerFormat(BaseModel):
    report: str
    links: List[str]

class ReportGenerator:
    def __init__(self):
        self.llm = AzureChatOpenAI(azure_deployment="gpt-4.1",temperature=0.2)
        self.prompt = ChatPromptTemplate.from_template("""
                                                       
        Based on the following information, create a short, comprehensive report!
        DO NOT include any additional commentary, guesses, or explanations.
        If there is no context provided, or if the provided context does not contain enough information to answer the question accurately OR if the question isn't about Graphisoft or its products YOU MUST respond with EXACTLY: "I don't have information about the question"

        The report should contain a complete, well structured Markdown report based only on the context. 
        The report must end with a '## References' section listing any links from the 'source' section of the chunk used in your summary.
        ONLY include links and nothing else in both '##Reference' section and links if they contributed to the final answer!
        The report must not exceed 250 words.

        QUERY:
        {original_query}

        CONTEXT:
        {context}
        """
        )

    def generate_report(self, original_query, selected_chunks):
        """
        Generate a Markdown report based on retrieved document chunks.
        
        :param original_query: The initial user query.
        :param selected_chunks: A list of (Document, score) tuples to use as context.
        :return: A clean, final Markdown report.
        """
        # Combine document content and sources into one context string
        context = "\n\n".join([(chunk.page_content + "\nsource:" + chunk.metadata["source"]) for chunk,_ in selected_chunks])

        # Create prompt for the report generation
        messages = self.prompt.format_messages(
            original_query=original_query,
            context=context
        )

        # Wrap the LLM with structured output format based on the AnswerFormat schema
        structured_chat = self.llm.with_structured_output(AnswerFormat)
        
        # Generate the report
        response = structured_chat.invoke(
            input = messages
        )
        
        return response