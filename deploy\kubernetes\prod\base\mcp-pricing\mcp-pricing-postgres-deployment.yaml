apiVersion: apps/v1
kind: Deployment
metadata:
  name: mcp-pricing-postgres
  labels:
    app: sales-agent
    component: pricing
    tier: db
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sales-agent
      component: pricing
      tier: db
      environment: production
  template:
    metadata:
      labels:
        app: sales-agent
        component: pricing
        tier: db
        environment: production
    spec:
      containers:
        - name: mcp-pricing-postgres
          image: postgres:16
          envFrom:
            - secretRef:
                name: mcp-pricing-postgres-credentials
          livenessProbe:
            exec:
              command: ["pg_isready", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          readinessProbe:
            exec:
              command: ["pg_isready", "-U", "postgres"]
            initialDelaySeconds: 5
            periodSeconds: 5
            timeoutSeconds: 1
            failureThreshold: 5
          ports:
            - containerPort: 5432
              protocol: TCP
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: mcp-pricing-data
      volumes:
        - name: mcp-pricing-data
          persistentVolumeClaim:
            claimName: mcp-pricing-data
