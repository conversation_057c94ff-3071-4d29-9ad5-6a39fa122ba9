from langchain_core.messages import BaseMessage
from typing import Sequence

def remove_tool_calls(
        messages: Sequence[BaseMessage],
        save_last: int = 0 # Number of last tool calls to save, 0 means delete all as soon as possible. Negative value means save all tool calls (default: 0)
        ) -> Sequence[BaseMessage]:
    
    if messages[-1].type == "tool" or save_last < 0:
        return messages

    tool_call_removed_messages = []

    keep_processing=True
    is_ai_tool_call = False
    tool_called_num = 0

    for message in reversed(messages):

        if not keep_processing or message.type == "human":
            keep_processing = False

        if not (is_ai_tool_call):

            if message.type != "tool" or keep_processing:
                tool_call_removed_messages.insert(0, message)
            else:
                tool_called_num += 1

                if tool_called_num <= save_last:
                    tool_call_removed_messages.insert(0, message)
                else:
                    is_ai_tool_call = True

        else:
            is_ai_tool_call = False

    return list(tool_call_removed_messages)