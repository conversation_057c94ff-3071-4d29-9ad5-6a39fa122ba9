apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: agent-chat-ui-ingress
  namespace: default
  annotations:
    traefik.ingress.kubernetes.io/router.middlewares: default-strip-api-middleware@kubernetescrd
spec:
  rules:
  - host: ""
    http:
      paths:
      - backend:
          service:
            name: agent-chat-ui
            port:
              number: 3000
        path: /
        pathType: Prefix
      - backend:
          service:
            name: langgraph-api
            port:
              number: 8123
        path: /api/
        pathType: Prefix
