import psycopg2

def ensure_vector_index_ivfflat(table_name, service_url, embedding_col="embedding"):
    """Create a vector index on the embedding column if it does not exist."""
    index_name = f"{table_name}_{embedding_col}_ivfflat_idx"
    create_index_sql = f"""
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes WHERE tablename = '{table_name}' AND indexname = '{index_name}'
        ) THEN
            EXECUTE 'CREATE INDEX {index_name} ON {table_name} USING ivfflat ({embedding_col} vector_l2_ops) WITH (lists = 100)';
        END IF;
    END
    $$;
    """
    try:
        conn = psycopg2.connect(service_url)
        conn.autocommit = True
        with conn.cursor() as cur:
            cur.execute(create_index_sql)
        conn.close()
    except Exception as e:
        print(f"[Warning] Could not create vector index: {e}")

def ensure_vector_index_hnsw(table_name, service_url, embedding_col="embedding"):
    """
    Create an HNSW index on the embedding column if it does not exist.
    Requires pgvector >= 0.5.0.
    """
    index_name = f"{table_name}_{embedding_col}_hnsw_idx"
    create_index_sql = f"""
    DO $$
    BEGIN
        IF NOT EXISTS (
            SELECT 1 FROM pg_indexes WHERE tablename = '{table_name}' AND indexname = '{index_name}'
        ) THEN
            EXECUTE 'CREATE INDEX {index_name} ON {table_name} USING hnsw ({embedding_col} vector_l2_ops)';
        END IF;
    END
    $$;
    """
    try:
        conn = psycopg2.connect(service_url)
        conn.autocommit = True
        with conn.cursor() as cur:
            cur.execute(create_index_sql)
        conn.close()
        print(f"[Info] HNSW index '{index_name}' ensured on table '{table_name}'.")
    except Exception as e:
        print(f"[Warning] Could not create HNSW index: {e}")
