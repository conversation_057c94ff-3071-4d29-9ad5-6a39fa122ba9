import os
from fastmcp import <PERSON><PERSON>P, Context
from contextlib import asynccontextmanager
from collections.abc import Async<PERSON>tera<PERSON>
from dataclasses import dataclass
from dotenv import load_dotenv

from vectorsearch import TimescaleVectorSearch


# Load environment variables from .env file
load_dotenv()

SERVICE_URL =  os.environ.get("VECTOR_STORE_URL")

# Create an MCP server
mcp = FastMCP("RAG search service")

@dataclass
class AppContext:
    vector_search: TimescaleVectorSearch

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with database connections"""
    vector_search = TimescaleVectorSearch(service_url=SERVICE_URL)
    try:
        yield AppContext(vector_search=vector_search)
    finally:
        pass

# Pass lifespan to server
mcp = FastMCP("RAG search service", lifespan=app_lifespan)

@mcp.tool()
def search_context(
    ctx: Context,
    query: str,
) -> list[tuple]:
    """
    This tool is used for searching and gathering information from the RAG database mainly about Graphisoft and its products.
    
    Args:
        query: Based on the given query, the tool will search for relevant information in the database with vectorsearch retriever.    
    Returns:
        A list of chunks which come from webpages and transformed into markdown format and the sources of the webpages where chunks come from.
    """
    vector_search = ctx.request_context.lifespan_context.vector_search

    # Perform a similarity search using all the rephrased queries
    results = vector_search.search_query(query=query, k=20)
    final_result = [(chunk, chunk.metadata["source"]) for chunk, _ in results]
    return final_result

if __name__ == "__main__":
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000)