services:
  timescaledb_with_pgvector:
    image: timescaledb-with-pgvector
    container_name: timescaledb-pgvector
    shm_size: 2gb
    restart: unless-stopped
    ports:
      - "5435:5432"
    environment:
      - POSTGRES_USER=your_user
      - POSTGRES_PASSWORD=your_password
      - POSTGRES_DB=timescaledb-pgvector
    volumes:
      - timescaledb_pgvector_data:/var/lib/postgresql/data
volumes:
  timescaledb_pgvector_data: