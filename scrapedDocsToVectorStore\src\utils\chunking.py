from langchain.text_splitter import RecursiveCharacterTextSplitter

from src.utils.doc_with_source import DocWithSource


class ChunkingUtils:
    def __init__(self, chunk_size=2000, chunk_overlap=400):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap

    def addChunkListsTo(self, docsWithContent: list[DocWithSource]):
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=self.chunk_size, chunk_overlap=self.chunk_overlap)

        for doc in docsWithContent:
            doc.chunkList=text_splitter.split_text(doc.content)