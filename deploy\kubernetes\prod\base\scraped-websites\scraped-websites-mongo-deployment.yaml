apiVersion: apps/v1
kind: Deployment
metadata:
  name: scraped-websites-mongo
  labels:
    app: graphibot
    component: datastore
    tier: db
    environment: production
spec:
  replicas: 1
  selector:
    matchLabels:
      app: graphibot
      component: datastore
      tier: db
      environment: production
  template:
    metadata:
      labels:
        app: graphibot
        component: datastore
        tier: db
        environment: production
    spec:
      containers:
        - name: scraped-websites-mongo
          image: mongo:8
          envFrom:
            - secretRef:
                name: scraped-websites-mongo-credentials
          ports:
            - containerPort: 27017
              protocol: TCP
          volumeMounts:
            - mountPath: /data/db
              name: scraped-websites-data
      volumes:
        - name: scraped-websites-data
          persistentVolumeClaim:
            claimName: scraped-websites-data
