import psycopg2

def get_latest_table_name_from_timescale(db_url: str, schema: str = 'public') -> str:
        """
        Args:
            db_url: Database connection URL for psycopg2.connect
            schema: Schema to search tables in (default: 'public')
            
        Returns:
            The latest table name from TimescaleDB with format 'rag_YYYYMMDD_HHMMSS'.
        """
        try:
            conn = psycopg2.connect(db_url)
            cur = conn.cursor()
            # Query all tables in the schema matching the pattern
            cur.execute(f"""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = %s
                AND table_name ~ '^rag_\\d{{8}}_\\d{{6}}$'
            """, (schema,))
            tables = [row[0] for row in cur.fetchall()]
            if not tables:
                return ""
            # Sort tables lexicographically (works for this format)
            latest_table = sorted(tables)[-1]

            return latest_table
        except Exception as e:
            print(f"Error: {e}")
            return ""
        finally:
            if 'cur' in locals():
                cur.close()
            if 'conn' in locals():
                conn.close()