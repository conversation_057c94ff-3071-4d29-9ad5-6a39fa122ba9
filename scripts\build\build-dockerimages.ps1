# Set error action to stop on any errors
$ErrorActionPreference = "Stop"

# Define the root directory of your sales-agent repository.
# Assuming this script is placed in 'sales-agent/scripts',
# $PSScriptRoot points to 'sales-agent/scripts'.
# We need to go up one level to get to 'sales-agent'.
$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName

Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "Starting Docker image build process..."

# --- Pricing MCP Server ---
Write-Host "`n--- Building Pricing MCP Server Image ---"
try {
    Push-Location (Join-Path $salesAgentRoot "pricing")
    docker build -t mcp-pricing .
    Pop-Location
    Write-Host "Pricing MCP Server image built successfully."
}
catch {
    Write-Host "Error building Pricing MCP Server image: $($_.Exception.Message)"
    exit 1
}

# --- Search MCP Server ---
Write-Host "`n--- Building Search MCP Server Image ---"
try {
    Push-Location (Join-Path $salesAgentRoot "search")
    docker build -t mcp-search .
    Pop-Location
    Write-Host "Search MCP Server image built successfully."
}
catch {
    Write-Host "Error building Search MCP Server image: $($_.Exception.Message)"
    exit 1
}

Write-Host "`n--- Building TimescaleDB with pgvector Image for Search ---"
try {
    Push-Location (Join-Path $salesAgentRoot "scrapedDocsToVectorStore\Docker")
    docker build -t timescaledb-with-pgvector -f ".\DockerImage\Dockerfile" .
    Pop-Location
    Write-Host "TimescaleDB with pgvector image built successfully."
}
catch {
    Write-Host "Error building TimescaleDB with pgvector image: $($_.Exception.Message)"
    exit 1
}

# --- Agent Chat UI ---
Write-Host "`n--- Building Agent Chat UI Image ---"
try {
    Push-Location (Join-Path $salesAgentRoot "agent-chat-ui")

    # Create .env file if it doesn't exist
    $envFilePath = Join-Path (Get-Location) ".env"
    if (-not (Test-Path $envFilePath)) {
        Write-Host "Creating .env file for Agent Chat UI..."
        @"
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_ASSISTANT_ID=agent
"@ | Set-Content $envFilePath
        Write-Host ".env file created."
    } else {
        Write-Host ".env file already exists for Agent Chat UI. Skipping creation."
    }

    docker build -t agent-chat-ui .
    Pop-Location
    Write-Host "Agent Chat UI image built successfully."
}
catch {
    Write-Host "Error building Agent Chat UI image: $($_.Exception.Message)"
    exit 1
}

# --- Langgraph API ---
Write-Host "`n--- Preparing and Building Langgraph API (sales-agent-api) ---"
try {
    Push-Location (Join-Path $salesAgentRoot "agent")

    Write-Host "Installing langgraph-cli..."
    pip install 'langgraph-cli[inmem]'

    Write-Host "Installing Langgraph API in editable mode..."
    pip install -e .

    Write-Host "Building Langgraph API (sales-agent-api)..."
    langgraph build -t sales-agent-api

    Pop-Location
    Write-Host "Langgraph API (sales-agent-api) built successfully."
}
catch {
    Write-Host "Error building Langgraph API: $($_.Exception.Message)"
    exit 1
}

Write-Host "`nAll specified Docker images have been processed."
