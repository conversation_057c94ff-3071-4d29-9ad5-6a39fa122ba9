# Set error action to stop on any errors
$ErrorActionPreference = "Stop"

# Define the root directory of your sales-agent repository
$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.FullName

Write-Host "Sales Agent Repository Root: $salesAgentRoot"

# --- Build phase ---
Write-Host "`n--- Running build scripts ---"
try {
    Push-Location (Join-Path $salesAgentRoot "scripts\build")

    Write-Host "Running build-dockerimages.ps1..."
    .\build-dockerimages.ps1
    Write-Host "build-dockerimages.ps1 completed."

    Write-Host "Running build-jobs-images.ps1..."
    .\build-jobs-images.ps1
    Write-Host "build-jobs-images.ps1 completed."

    Pop-Location
}
catch {
    Write-Error "Error during build phase: $($_.Exception.Message)"
    exit 1
}

# --- Deploy phase ---
Write-Host "`n--- Running deploy scripts ---"
try {
    Push-Location (Join-Path $salesAgentRoot "scripts\deploy")

    Write-Host "Running persistence-deploy.ps1..."
    .\persistence-deploy.ps1
    Write-Host "persistence-deploy.ps1 completed."

    Write-Host "Running base-deploy.ps1..."
    .\base-deploy.ps1
    Write-Host "base-deploy.ps1 completed."

    Write-Host "Running jobs-deploy.ps1..."
    .\jobs-deploy.ps1
    Write-Host "jobs-deploy.ps1 completed."

    Pop-Location
}
catch {
    Write-Error "Error during deploy phase: $($_.Exception.Message)"
    exit 1
}

# --- Final port-forward ---
Write-Host "`n--- Starting port forward ---"
try {
    kubectl port-forward services/agent-chat-ui 3000:3000
}
catch {
    Write-Error "Error starting port forward: $($_.Exception.Message)"
    exit 1
}

# Prevent the window from closing immediately
Write-Host "`nAll steps completed. Press Enter to exit..."
[void][System.Console]::ReadLine()
