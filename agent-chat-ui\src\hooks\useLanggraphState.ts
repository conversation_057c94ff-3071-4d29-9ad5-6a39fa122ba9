import { useEffect, useState } from "react";
import { State } from "@/types/langgraph";
import { Message } from "@langchain/langgraph-sdk";


export function useLanggraphState(threadId: string | null, messages: number) {

    const [state, setState] = useState<State | null>(null);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (!threadId) {
            setState(null);
            return;
        }

        const fetchState = async () => {
            setLoading(true);
            try {
                const res = await fetch(`/api/threads/${threadId}/state`);
                if (!res.ok) throw new Error("Failed to fetch state");
                const data = await res.json();
                // Extract the latest update_offer tool call
                const offerState: State = { offer: {}, price: null };
                if (data?.values?.messages) {

                    const toolCalls = data.values.messages
                        .flatMap((msg: any) => msg.tool_calls || []);
                    // Find the last update_offer tool call
                    const lastUpdateOffer = [...toolCalls].reverse().find(
                        (tc: any) => tc.name === "update_offer"
                    );
                    const lastUpdatePrice = [...toolCalls].reverse().find(
                        (tc: any) => tc.name === "update_price"
                    );
                    if (lastUpdateOffer && lastUpdateOffer.args?.params && lastUpdatePrice && lastUpdatePrice.args?.price) {
                        offerState.offer = lastUpdateOffer.args.params;
                        offerState.price = lastUpdatePrice.args.price;
                    }
                }
                setState(offerState);
                console.log("State fetched:", state);
            } catch (error) {
                console.error("Error fetching state:", error);
                setState(null);
            } finally {
                setLoading(false);
            }
        };

        fetchState();
    }, [messages, threadId]);

    return { state, loading };
}