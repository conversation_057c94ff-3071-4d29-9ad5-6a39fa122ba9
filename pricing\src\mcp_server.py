# mcp_server.py
import os
from fastmcp import FastMCP, Context
from contextlib import asynccontextmanager
from collections.abc import AsyncIterator
from dataclasses import dataclass
from pricing_repository import ProductRepository
from pricing_calculator import PricingCalculator
from dotenv import load_dotenv
from countries import countries

# Create an MCP server
mcp = FastMCP("Pricing Service")

# Database configuration - should be moved to config file in production
DB_CONFIG = {
    "dbname": os.getenv("POSTGRES_DB", "your_db_name"),
    "user": os.getenv("POSTGRES_USER", "your_user"),
    "password": os.getenv("POSTGRES_PASSWORD", "your_password"),
    "host": os.getenv("POSTGRES_HOST", "localhost"),
    "port": os.getenv("POSTGRES_PORT", "5432")
}

@dataclass
class AppContext:
    pricing_calculator: PricingCalculator
    repository: ProductRepository

@asynccontextmanager
async def app_lifespan(server: FastMCP) -> AsyncIterator[AppContext]:
    """Manage application lifecycle with database connections"""
    repository = ProductRepository(DB_CONFIG)
    pricing_calculator = PricingCalculator(repository)
    try:
        yield AppContext(pricing_calculator=pricing_calculator, repository=repository)
    finally:
        repository.close()

# Pass lifespan to server
mcp = FastMCP("Pricing Service", lifespan=app_lifespan)

# Tool to calculate product pricing
@mcp.tool()
def calculate_price(subscription: str, country_code: str, term: str, seats: int, ctx: Context) -> str:
    """
    Calculate the total price for a product subscription. If you don't know the exact product name, you can use the `get_subscriptions` tool to list available products.
    
    Args:
        subscription: The product the user is interested in
        country_code: Two letter code of the country the user is in.
        term: term of the subscription, allowed values are: monthly, yearly, or 3 years)
        seats: Number of seats to be purchased
    
    Returns:
        Pricing information including total price
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    
    try:
        result = pricing_calculator.get_total_price(
            product_name=subscription,
            country=country_code,
            subscription=term,
            num_users=seats
        )
        return str(result)
    except ValueError as e:
        return f"Error: {str(e)}"

# Tool to list available subscription options
@mcp.tool()
def list_term_options(ctx: Context) -> str:
    """List all available terms
    Args:
        ctx: Context object containing request information
    Returns:
        str: List of available terms
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    options = pricing_calculator.list_subscription_options()
    return str(options)

# Tool to list available countries
@mcp.tool()
def list_countries(ctx: Context) -> str:
    """List all countries where pricing is available
    Args:
        ctx: Context object containing request information
    Returns:
        json with available countries :
        {
            "country_code": "country_name",
            ...
        }
    """

    return str(countries)

# Resource for pricing by country
@mcp.tool()
def get_country_pricing(country_code: str, ctx: Context) -> str:
    """Get pricing options for a specific country
    Args:
        country_code: two letter code of the user's country (e.g., US, UK)
        ctx: Context object containing request information
    Returns:
        str: Pricing information for the specified country
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    pricing = pricing_calculator.get_pricing_by_country(country_code)
    return str(pricing)

@mcp.tool()
def get_subscriptions(ctx: Context, country_code = None) -> str:
    """Get all subscriptions (products) available in a specific country or all subscriptions if no country is specified
    Args:
        ctx: Context object containing request information
        country_code: two letter code of the user's country (e.g., US, UK)(optional)
    Returns:
        str: List of all products
    """
    pricing_calculator = ctx.request_context.lifespan_context.pricing_calculator
    if country_code is None:
        products = pricing_calculator.repository.get_all_products()
    else:  # Default to the first available country
        products = pricing_calculator.repository.get_all_awailable_products(country_code)
    return str(products)

if __name__ == "__main__":
    mcp.run(transport="streamable-http", host="0.0.0.0", port=8000)