$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "`n--- Deleting Kubernetes resources: jobs ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\jobs")
    kubectl delete -f .
    Pop-Location
    Write-Host "Resources from deploy\kubernetes\dev\jobs deleted successfully."
}
catch {
    Write-Error "Error deleting job resources: $($_.Exception.Message)"
    exit 1
}
