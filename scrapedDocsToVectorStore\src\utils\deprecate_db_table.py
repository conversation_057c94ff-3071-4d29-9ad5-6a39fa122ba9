import psycopg2
from psycopg2 import sql

from src.utils.generate_table_name import generate_table_name
from src.utils.ts_service_url import get_vector_store_service_url

import os
from dotenv import load_dotenv
load_dotenv()

def ts_deprecate_db_table(old_table_name=os.getenv("VECTOR_STORE_COLLECTION"), 
                          new_table_name=generate_table_name(os.getenv("VECTOR_STORE_COLLECTION")), 
                          service_url=get_vector_store_service_url()):
    conn = None
    cur = None
    try:
        conn = psycopg2.connect(service_url)
        cur = conn.cursor()

        find_indexes_query = sql.SQL("SELECT indexname FROM pg_indexes WHERE tablename = %s")
        cur.execute(find_indexes_query, (old_table_name,))
        
        indexes = cur.fetchall()
        
        # 2. Loop through each index and rename it.
        for (index_name,) in indexes:
            new_index_name = index_name.replace(old_table_name, new_table_name)

            if new_index_name != index_name:
                print(f"Renaming index '{index_name}' to '{new_index_name}'...")

                rename_index_query = sql.SQL("ALTER INDEX IF EXISTS {old_idx} RENAME TO {new_idx}").format(
                    old_idx=sql.Identifier(index_name),
                    new_idx=sql.Identifier(new_index_name)
                )
                cur.execute(rename_index_query)

        # 3. Finally, rename the table itself.
        print(f"Renaming table '{old_table_name}' to '{new_table_name}'...")
        rename_table_query = sql.SQL("ALTER TABLE IF EXISTS {old_tbl} RENAME TO {new_tbl}").format(
            old_tbl=sql.Identifier(old_table_name),
            new_tbl=sql.Identifier(new_table_name)
        )
        cur.execute(rename_table_query)

        # 4. Commit the transaction if all steps were successful.
        conn.commit()
        print("Table and all associated indexes renamed successfully.")

    except (Exception, psycopg2.DatabaseError) as error:
        print(f"Error during database operation: {error}")
        # Roll back the transaction in case of an error
        if conn:
            conn.rollback()
    
    finally:
        # Ensure the cursor and connection are always closed.
        if cur:
            cur.close()
        if conn:
            conn.close()