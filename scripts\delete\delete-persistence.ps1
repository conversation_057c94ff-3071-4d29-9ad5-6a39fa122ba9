$ErrorActionPreference = "Stop"

$salesAgentRoot = (Get-Item -Path $PSScriptRoot).Parent.Parent.FullName
Write-Host "Sales Agent Repository Root: $salesAgentRoot"

Write-Host "`n--- Deleting Kubernetes resources: persistence ---"
try {
    Push-Location (Join-Path $salesAgentRoot "deploy\kubernetes\dev\persistence")
    kubectl delete -f .
    Pop-Location
    Write-Host "Resources from deploy\kubernetes\dev\persistence deleted successfully."
}
catch {
    Write-Error "Error deleting persistence resources: $($_.Exception.Message)"
    exit 1
}
